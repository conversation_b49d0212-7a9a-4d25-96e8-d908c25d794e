from __future__ import annotations
from autogen import ConversableAgent, register_function
import os, sys, re, ast
from typing import Dict, List, get_type_hints

SCORE_KEYWORDS: dict[int, list[str]] = {
    1: ["awful", "horrible", "disgusting"],
    2: ["bad", "unpleasant", "offensive"],
    3: ["average", "uninspiring", "forgettable"],
    4: ["good", "enjoyable", "satisfying"],
    5: ["awesome", "incredible", "amazing"]
}

# ────────────────────────────────────────────────────────────────
# 0. OpenAI API key setup ── *Do **not** modify this block.*
# ────────────────────────────────────────────────────────────────
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "").strip()
if not OPENAI_API_KEY:
    sys.exit("❗ Set the OPENAI_API_KEY environment variable first.")
LLM_CFG = {"config_list": [{"model": "gpt-4o-mini", "api_key": OPENAI_API_KEY}]}

# ────────────────────────────────────────────────────────────────
# 1. Utility data structures & helper functions
# ────────────────────────────────────────────────────────────────

def normalize(text: str) -> str:
    return re.sub(r"\s+", " ", re.sub(r"[^\w\s]", " ", text.lower())).strip()

def fetch_restaurant_data(restaurant_name: str) -> dict[str, list[str]]:
    data = {}
    target = normalize(restaurant_name)

    index = 1
    with open(DATA_PATH, encoding="utf-8") as f:
        for line in f:
            if not line.strip(): continue
            name, review = line.split('.', 1)
            if normalize(name) == target:
                data.setdefault(name.strip(), []).append({f"Review {index}": review.strip()})
                index += 1
    return data


def calculate_overall_score(restaurant_name: str, food_scores: List[int], customer_service_scores: List[int]) -> dict[str, str]:
    """Geometric-mean rating rounded to 3 dp."""
    n = len(food_scores)
    if n == 0 or n != len(customer_service_scores):
        raise ValueError("food_scores and customer_service_scores must be non-empty and same length")
    total = sum(((f**2 * s)**0.5) * (1 / (n * (125**0.5))) * 10 for f, s in zip(food_scores, customer_service_scores))
    return {restaurant_name: f"{total:.3f}"}

# register functions
fetch_restaurant_data.__annotations__ = get_type_hints(fetch_restaurant_data)
calculate_overall_score.__annotations__ = get_type_hints(calculate_overall_score)

# ──────────────────────────────────────────────
# 2. Agent setup
# ──────────────────────────────────────────────

def build_agent(name, msg):
    return ConversableAgent(name=name, system_message=msg, llm_config=LLM_CFG)

DATA_FETCH = build_agent(
    "fetch_agent",
    'Return JSON {"call":"fetch_restaurant_data","args":{"restaurant_name":"<name>"}}'
)
ANALYZER_PROMPT = (
    "You are a meticulous Restaurant Review Analyzer. Your task is to analyze restaurant reviews to extract food and service scores. "
    "You MUST process EVERY SINGLE review provided in the input. Do not summarize, skip, or truncate the list of reviews before generating the final score lists.\n\n"
    "Follow this two-step process for EACH review:\n\n"
    "STEP 1: KEYWORD IDENTIFICATION & NORMALIZATION\n"
    "   a. For each review, diligently locate exactly one adjective that best describes the FOOD quality and exactly one adjective that best describes the CUSTOMER SERVICE.\n"
    "   a.1. If a review contains multiple adjectives for food or service (e.g., 'The food was amazing and delicious, service was quick and friendly'), select the single adjective that most strongly and directly represents the overall sentiment for that aspect in that specific review. Pay attention to all parts of the review sentence or paragraph to make the best selection.\n"
    "   b. The words you find in the review (let's call them 'review keywords') might be exact matches, common synonyms, or typical misspellings of the 'canonical keywords' listed below.\n"
    "   c. Your goal is to identify the review keyword and then determine which canonical keyword it corresponds to. If the review keyword is not an exact match to a canonical keyword, you MUST briefly state your reasoning (e.g., 'horible' is a misspelling of 'horrible'; 'decent' is a synonym for 'average'; 'quick' for service maps to 'good' if it implies positive efficiency).\n"
    "   d. The predefined list of canonical keywords and their scores is:\n"
    f"      {SCORE_KEYWORDS}\n"
    "   d.0. SPECIFIC CANONICAL MAPPINGS TO NOTE (Refer to SCORE_KEYWORDS for scores):\n" # New subsection
    "      - 'unpleasant' for FOOD or SERVICE maps to 'unpleasant' (Score 2).\n"
    "      - 'forgettable' for FOOD maps to 'forgettable' (Score 3).\n"
    "      - 'uninspiring' for FOOD maps to 'uninspiring' (Score 3).\n"
    "      - 'average' for FOOD or SERVICE maps to 'average' (Score 3).\n"
    "      - For food quality, terms like 'decent', 'fine', 'passable', 'okay', 'nothing special', 'middling', 'standard', 'fair', 'meh' should generally be mapped to 'average' (Score 3). If 'decent' or 'fine' for food is used in a clearly positive context without other negative qualifiers, you may map to 'good' (Score 4), and state your reasoning.\n"
    "      - For service, 'standard' or 'so-so' maps to 'average' (Score 3). Terms like 'subpar', 'poor', 'not great', 'mediocre' should map to 'bad' (Score 2). A term like 'yucky' should map to 'disgusting' (Score 1).\n"
    "   d.1. CRITICAL MAPPING FOR STRONG POSITIVES: When a review keyword is a very strong positive descriptor (e.g., 'stellar', 'phenomenal', 'god-tier', 'superb', 'legendary', 'chef's kiss', 'mind-blowing', 'top-notch', 'out-of-this-world', 'heavenly', 'divine', 'exquisite') but not an exact match in the canonical list, you MUST map it to a Score 5 canonical keyword (i.e., 'awesome', 'incredible', or 'amazing'). Provide reasoning for this mapping. Do NOT map such intensely positive terms to lower scores like 'good' unless the surrounding context explicitly and unequivocally diminishes their intensity.\n"
    "   e. Ensure you find and map exactly one canonical food-related keyword and one canonical service-related keyword PER REVIEW.\n\n"
    "STEP 2: SCORE ASSIGNMENT\n"
    "   a. Using the canonical keywords identified and normalized in Step 1 (especially adhering to d.0 and d.1), assign the corresponding numerical score based on the mapping provided in `SCORE_KEYWORDS`.\n\n"
    "OUTPUT FORMAT:\n"
    "First, for EACH review, detail your keyword identification, normalization, and scoring process. Group details per review clearly.\n"
    "Example for processing multiple reviews:\n"
    "Review 1: \"The burger was awesome and the waiter was very good.\"\n"
    "  Food Keyword (in review): awesome\n"
    "  Canonical Food Keyword: awesome (Reasoning: exact match)\n"
    "  Food Score: 5\n"
    "  Service Keyword (in review): good\n"
    "  Canonical Service Keyword: good (Reasoning: exact match)\n"
    "  Service Score: 4\n\n"
    "Review 2: \"Food was ok, service kinda bad. Staff seemed rushed.\"\n"
    "  Food Keyword (in review): ok\n"
    "  Canonical Food Keyword: average (Reasoning: 'ok' is a common synonym for 'average' as per d.0)\n"
    "  Food Score: 3\n"
    "  Service Keyword (in review): bad\n"
    "  Canonical Service Keyword: bad (Reasoning: exact match, 'rushed' supports negative service sentiment)\n"
    "  Service Score: 2\n\n"
    "Review 3: \"The pasta was stellar, truly chef's kiss! Service was incredible.\"\n"
    "  Food Keyword (in review): stellar (also noted 'chef's kiss')\n"
    "  Canonical Food Keyword: incredible (Reasoning: 'stellar' and 'chef's kiss' are strong positives, mapping to Score 5 as per d.1)\n"
    "  Food Score: 5\n"
    "  Service Keyword (in review): incredible\n"
    "  Canonical Service Keyword: incredible (Reasoning: exact match)\n"
    "  Service Score: 5\n\n"
    "SELF-CHECK AND FINAL LISTS:\n"
    "After meticulously processing ALL reviews in the detailed manner shown above, AND BEFORE providing the final score lists, perform a self-check: Ensure your detailed breakdown (Food Keyword, Canonical Food Keyword, Food Score, Service Keyword, Canonical Service Keyword, Service Score) covers EVERY SINGLE review from start to end. \n" # Self-check instruction
    "Then, compile the `food_scores` and `customer_service_scores` lists. These lists MUST be a direct and accurate transcription of the 'Food Score' and 'Service Score' you assigned in your per-review detailed breakdown. No scores should be assumed or defaulted for reviews at the end of the list; each must come from its specific analysis.\n"
    "Provide the final scores in this EXACT format on new lines. These lists MUST contain all extracted scores:\n"
    "food_scores=[score_review1, score_review2, ..., score_reviewN]\n"
    "customer_service_scores=[score_review1, score_review2, ..., score_reviewN]\n\n"
    "CRITICAL GUIDELINES:\n"
    "- YOU MUST PROCESS EVERY SINGLE REVIEW. The number of scores in `food_scores` and `customer_service_scores` MUST exactly match the total number of reviews provided to you.\n"
    "- Adhere strictly to the canonical keywords and their scores provided, especially the mapping rules in d.0 and d.1.\n"
    "- If a review keyword is an exact match to a canonical keyword, the reasoning can be simply 'exact match'."
)

ANALYZER = build_agent("review_analyzer_agent", ANALYZER_PROMPT)
SCORER = build_agent(
    "scoring_agent",
    "You receive restaurant name and two score lists (food_scores, customer_service_scores).\n"
    "Call calculate_overall_score with the restaurant name and both score lists.\n"
    "Use the exact restaurant name provided in the context."
)
ENTRY = build_agent("entry", "Coordinator")

# register functions
register_function(
    fetch_restaurant_data,
    caller=DATA_FETCH,
    executor=ENTRY,
    name="fetch_restaurant_data",
    description="Fetch reviews from specified data file by name.",
)
register_function(
    calculate_overall_score,
    caller=SCORER,
    executor=ENTRY,
    name="calculate_overall_score",
    description="Compute final rating via geometric mean.",
)


# ────────────────────────────────────────────────────────────────
# 3. Conversation helpers
# ────────────────────────────────────────────────────────────────

def run_chat_sequence(entry: ConversableAgent, sequence: list[dict]) -> str:
    ctx = {**getattr(entry, "_initiate_chats_ctx", {})}
    for step in sequence:
        msg = step["message"].format(**ctx)
        chat = entry.initiate_chat(
            step["recipient"], message=msg,
            summary_method=step.get("summary_method", "last_msg"),
            max_turns=step.get("max_turns", 2),
        )
        out = chat.summary
        # Data fetch output
        if step["recipient"] is DATA_FETCH:
            for past in reversed(chat.chat_history):
                try:
                    data = ast.literal_eval(past["content"])
                    if isinstance(data, dict) and data and not ("call" in data):
                        ctx.update({"reviews_dict": data, "restaurant_name": next(iter(data))})
                        break
                except:
                    continue
        # Analyzer output passed directly
        elif step["recipient"] is ANALYZER:
            ctx["analyzer_output"] = out
    return out

ConversableAgent.initiate_chats = lambda self, seq: run_chat_sequence(self, seq)

# ──────────────────────────────────────────────
# 4. Main entry
# ──────────────────────────────────────────────

def main(user_query: str, data_path: str = "restaurant-data.txt"):
    global DATA_PATH
    DATA_PATH = data_path
    agents = {"data_fetch": DATA_FETCH, "analyzer": ANALYZER, "scorer": SCORER}
    chat_sequence = [
        {"recipient": agents["data_fetch"],
         "message": "Find reviews for this query: {user_query}",
         "summary_method": "last_msg",
         "max_turns": 2},

        {"recipient": agents["analyzer"],
         "message": "Here are the reviews from the data fetch agent:\n{reviews_dict}\n\nExtract food and service scores for each review.",
         "summary_method": "last_msg",
         "max_turns": 1},

        {"recipient": agents["scorer"],
         "message": "Restaurant: {restaurant_name}\n{analyzer_output}",
         "summary_method": "last_msg",
         "max_turns": 2},
    ]
    ENTRY._initiate_chats_ctx = {"user_query": user_query}
    result = ENTRY.initiate_chats(chat_sequence)
    print(f"result: {result}")
    return result

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print('Usage: python main.py path/to/data.txt "How good is Subway?" ')
        sys.exit(1)

    path = sys.argv[1]
    query = sys.argv[2]
    main(query, path)
